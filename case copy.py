# -- coding: utf-8 --
# @Time : 2024-9-23 14:35:51
# <AUTHOR> lirunfang
import sys
import KatyushaDriver

args = sys.argv
driver = KatyushaDriver.Katyusha(args)
driver.stage('First', 'First')
driver.execute_action(
    action_name='LaunchBrowser',
    desc='打开浏览器',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '',
        'Arg': {
            'BrowserType': 'Chrome',
            'IsSetMax': True,
            'DeviceType': '',
            'BrowserName': '',
            'ConnectionOptions': '',
            'ExecutablePath': '',
        },
    },
)

driver.execute_action(
    action_name='AddCookies',
    desc='添加Cookie',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '',
        'Arg': {
            'BrowserSelector': '0.0.0', 
            'Cookies': [{'name': 'weboffice_branch', 'value': 'kdocs-amd-master-kso-v12', 'domain': '.kdocs.cn', 'path': '/'}, {'name': 'wps_sid', 'value': 'V02S8fLU4bfB2-Et1W2z4ukY8xPUxAY00a6db3e2000ee6bb60', 'domain': '.kdocs.cn', 'path': '/'}, {'name': 'csrf', 'value': 'QJhHfpeKJMZwyWaDxTQdBrpKSjQNjrh8', 'domain': '365.kdocs.cn', 'path': '/'}]
            },
    },
)
driver.execute_action(
    action_name='GoTo',
    desc='打开页面',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={
        'Timeout': 180,
        'Descr': '',
        'Arg': {
            'BrowserSelector': '0.0.0',
            'Url': 'https://www.kdocs.cn/l/crwRhAjXnn6p',
            'WaitUntil': 'load',
            'Referer': 'none',
        },
    },
)

driver.execute_action(
    action_name='CustomAction',
    desc='选中指定对象',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '',
        'Arg': {
            'customActionType': 'CUSTOM',
            'customActionIdxId': 'wpp_select_shape',
            'InstanceIndex': '0',
            'BrowserIndex': 1,
            'BrowserSelector': '0.0.0',
            'ObjectID': 0,
            'BrowserType': '{{DefaultBrowserType}}',
        },
    },
)

driver.execute_action(
    action_name='CustomAction',
    desc='选择已选中对象内的文本',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '',
        'Arg': {
            'customActionType': 'CUSTOM',
            'customActionIdxId': 'wpp_seleced_object_text',
            'InstanceIndex': '0',
            'BrowserIndex': 1,
            'BrowserSelector': '0.0.0',
            'Start': 0,
            'End': 9,
            'BrowserType': '{{DefaultBrowserType}}',
        },
    },
)

driver.execute_action(
    action_name='CustomAction',
    desc='设置选中的文本对齐方式',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={
        'Timeout': 60,
        'Descr': '',
        'Arg': {
            'customActionType': 'CUSTOM',
            'customActionIdxId': 'wpp_settext_anchor_type',
            'InstanceIndex': '0',
            'BrowserIndex': 1,
            'BrowserSelector': '0.0.0',
            'AnchorType': 0,
            'BrowserType': '{{DefaultBrowserType}}',
        },
    },
)



driver.execute_action(
    action_name='QuitBrowser',
    desc='关闭浏览器',
    app_type='WO_PC_V2',
    is_must_execute=False,
    is_must_pass=True,
    args={'Timeout': 60, 'Descr': '', 'Arg': {'BrowserSelector': '*'}},
)
driver.quit()

