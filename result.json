[{"name": "1、另存样张“合并表_合并规则”；", "desc": "1、另存样张“合并表_合并规则”；", "status": "Passed", "components": [{"status": "Passed", "actual": "None", "expected": "", "desc": "打开浏览器", "component_name": "LaunchBrowser", "component_desc": "打开浏览器", "screen_shot": "", "additional": "", "err_msg": ""}, {"status": "Passed", "actual": "[{'name': 'weboffice_branch', 'value': 'kdocs-amd-master-kso-v12', 'domain': '.kdocs.cn', 'path': '/'}, {'name': 'wps_sid', 'value': 'V02S8fLU4bfB2-Et1W2z4ukY8xPUxAY00a6db3e2000ee6bb60', 'domain': '.kdocs.cn', 'path': '/'}, {'name': 'csrf', 'value': 'QJhHfpeKJMZwyWaDxTQdBrpKSjQNjrh8', 'domain': '365.kdocs.cn', 'path': '/'}]", "expected": "", "desc": "添加<PERSON><PERSON>", "component_name": "AddCookies", "component_desc": "添加<PERSON><PERSON>", "screen_shot": "", "additional": "", "err_msg": ""}, {"status": "Passed", "actual": "None", "expected": "", "desc": "打开页面", "component_name": "GoTo", "component_desc": "打开页面", "screen_shot": "", "additional": "", "err_msg": ""}, {"status": "Passed", "actual": "None", "expected": "", "desc": "延时等待", "component_name": "WaitTimeout", "component_desc": "延时等待", "screen_shot": "", "additional": "", "err_msg": ""}]}, {"name": "2、新建文件，点击新建-合并数据表-从其他文件选择的数据表-选择样张；", "desc": "2、新建文件，点击新建-合并数据表-从其他文件选择的数据表-选择样张；", "status": "Passed", "components": [{"status": "Passed", "actual": "None", "expected": "", "desc": "创建页面", "component_name": "NewPage", "component_desc": "创建页面", "screen_shot": "", "additional": "", "err_msg": ""}, {"status": "Passed", "actual": "{'id': 100188416096, 'link_id': 'cseo0plrQI90', 'link_url': 'https://kdocs.cn/l/cseo0plrQI90', 'cache': 0, 'branch_id': 0, 'file_name': 'DB-HBB-00000011(19).dbt'}", "expected": "", "desc": "创建云文档", "component_name": "CreateWoDoc", "component_desc": "创建云文档", "screen_shot": "", "additional": "", "err_msg": ""}, {"status": "Passed", "actual": "[{'name': 'weboffice_branch', 'value': 'kdocs-amd-master-kso-v12', 'domain': '.kdocs.cn', 'path': '/'}, {'name': 'wps_sid', 'value': 'V02S8fLU4bfB2-Et1W2z4ukY8xPUxAY00a6db3e2000ee6bb60', 'domain': '.kdocs.cn', 'path': '/'}, {'name': 'csrf', 'value': 'QJhHfpeKJMZwyWaDxTQdBrpKSjQNjrh8', 'domain': '365.kdocs.cn', 'path': '/'}]", "expected": "", "desc": "添加<PERSON><PERSON>", "component_name": "AddCookies", "component_desc": "添加<PERSON><PERSON>", "screen_shot": "", "additional": "", "err_msg": ""}, {"status": "Passed", "actual": "None", "expected": "", "desc": "打开页面", "component_name": "GoTo", "component_desc": "打开页面", "screen_shot": "", "additional": "", "err_msg": ""}, {"status": "Passed", "actual": "None", "expected": "", "desc": "延时等待", "component_name": "WaitTimeout", "component_desc": "延时等待", "screen_shot": "", "additional": "", "err_msg": ""}, {"status": "Passed", "actual": "{\"sheetsInfo\": [{\"sheetItems\": [{\"fieldMappingList\": {\"tarFieldId\": [\"G\", \"F\"], \"srcFieldId\": [\"g\", \"W\"]}, \"srcSheetId\": 1}, {\"fieldMappingList\": {\"tarFieldId\": [\"J\", \"I\", \"H\"], \"srcFieldId\": [\"e\", \"a\", \"Y\"]}, \"srcSheetId\": 3}], \"fileId\": \"100188412578\"}], \"fldSourceFileInfoId\": \"\", \"targetSheetId\": 2, \"callBackId\": 1, \"fieldSourceId\": \"\", \"cmdName\": \"dbSheet.addSyncMergeSheet\"}", "expected": "", "desc": "点击新建-合并数据表，勾选数据表“不合并”，“不合并1”，点击合并", "component_name": "EvaluateJS", "component_desc": "点击新建-合并数据表，勾选数据表“不合并”，“不合并1”，点击合并", "screen_shot": "", "additional": "", "err_msg": ""}, {"status": "Passed", "actual": "V02S8fLU4bfB2-Et1W2z4ukY8xPUxAY00a6db3e2000ee6bb60", "expected": "", "desc": "获取cookies的wps_sid信息", "component_name": "EvaluateJS", "component_desc": "获取cookies的wps_sid信息", "screen_shot": "", "additional": "", "err_msg": ""}, {"status": "Passed", "actual": "kdocs-amd-master-kso-v12", "expected": "", "desc": "获取接口分支信息", "component_name": "EvaluateJS", "component_desc": "获取接口分支信息", "screen_shot": "", "additional": "", "err_msg": ""}, {"status": "Passed", "actual": "itkPbymMxsbcj7Q8amQJs8YeMsAAKB4N", "expected": "", "desc": "获取csrf信息", "component_name": "EvaluateJS", "component_desc": "获取csrf信息", "screen_shot": "", "additional": "", "err_msg": ""}]}, {"name": "3、勾选数据表“不合并”，“不合并1”，点击合并；", "desc": "3、勾选数据表“不合并”，“不合并1”，点击合并；", "status": "Passed", "components": [{"status": "Passed", "actual": "True", "expected": "", "desc": "发送HTTP请求", "component_name": "SendHttpRequest", "component_desc": "发送HTTP请求", "screen_shot": "", "additional": "", "err_msg": ""}, {"status": "Passed", "actual": "None", "expected": "", "desc": "等待内核&绘制完成", "component_name": "CustomAction", "component_desc": "等待内核&绘制完成", "screen_shot": "", "additional": "", "err_msg": ""}]}, {"name": "4、查看合并表内的“数字”，“数字1”字段；", "desc": "4、查看合并表内的“数字”，“数字1”字段；", "status": "Passed", "components": [{"status": "Passed", "actual": "true", "expected": "", "desc": "切换至新建合并表", "component_name": "EvaluateJS", "component_desc": "切换至新建合并表", "screen_shot": "", "additional": "", "err_msg": ""}, {"status": "Passed", "actual": "None", "expected": "", "desc": "延时等待", "component_name": "WaitTimeout", "component_desc": "延时等待", "screen_shot": "", "additional": "", "err_msg": ""}, {"status": "Passed", "actual": "数字", "expected": "数字", "desc": "检查合并表内显示2个数字字段图标字段：“数字”", "component_name": "CheckEvaluateJS", "component_desc": "检查合并表内显示2个数字字段图标字段：“数字”", "screen_shot": "", "additional": "", "err_msg": ""}, {"status": "Passed", "actual": "数字1", "expected": "数字1", "desc": "检查合并表内显示2个数字字段图标字段：“数字1”", "component_name": "CheckEvaluateJS", "component_desc": "检查合并表内显示2个数字字段图标字段：“数字1”", "screen_shot": "", "additional": "", "err_msg": ""}, {"status": "Passed", "actual": "1 ", "expected": "1 ", "desc": "检查“数字”字段第1行显示“1”", "component_name": "CheckEvaluateJS", "component_desc": "检查“数字”字段第1行显示“1”", "screen_shot": "", "additional": "", "err_msg": ""}, {"status": "Passed", "actual": "2 ", "expected": "2 ", "desc": "检查“数字1”字段第7行显示“2”", "component_name": "CheckEvaluateJS", "component_desc": "检查“数字1”字段第7行显示“2”", "screen_shot": "", "additional": "", "err_msg": ""}, {"status": "Passed", "actual": "None", "expected": "", "desc": "延时等待", "component_name": "WaitTimeout", "component_desc": "延时等待", "screen_shot": "", "additional": "", "err_msg": ""}, {"status": "Passed", "actual": "None", "expected": "", "desc": "关闭浏览器", "component_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "component_desc": "关闭浏览器", "screen_shot": "", "additional": "", "err_msg": ""}]}]